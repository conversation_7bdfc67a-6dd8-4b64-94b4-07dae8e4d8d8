.linear-bg(@color) {
  background-image: linear-gradient(to right, @color, @color 80%, fade(@color, 0) 100%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
}

body {
  font-size: 18px;
  color: #1e1f20;
  transform: scale(1.3);
  transform-origin: 0 0;
  width: 600px;
}

.container {
  width: 600px;
  padding: 10px 0 10px 0;
  background-size: 100% 100%;

}

.log-cont {
  background-size: cover;
  margin: 5px 15px 5px 10px;
  border-radius: 10px;

  .cont {
    margin: 0;
  }

  .cont-title {
    font-size: 16px;
    padding: 10px 20px 6px;

    &.current-version {
      font-size: 20px;
    }
  }

  .cont-body {
  }

  ul {
    font-size: 14px;
    padding-left: 20px;

    li {
      margin: 3px 0;
    }

    &.sub-log-ul {
      li {
        margin: 1px 0;
      }
    }
  }

  .cmd {
    color: #d3bc8e;
    display: inline-block;
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.5);
    padding: 0 3px;
    margin: 1px 2px;
  }

  .strong {
    color: #24d5cd;
  }

  .new {
    display: inline-block;
    width: 18px;
    margin: 0 -3px 0 1px;
  }

  .new:before {
    content: "NEW";
    display: inline-block;
    transform: scale(0.6);
    transform-origin: 0 0;
    color: #d3bc8e;
    white-space: nowrap;
  }
}

.dev-cont {
  background: none;

  .cont-title {
    background: rgba(0, 0, 0, .7);
  }

  .cont-body {
    background: rgba(0, 0, 0, .5);

    &.dev-info {
      background: rgba(0, 0, 0, .2);
    }
  }

  .strong {
    font-size: 15px;
  }
}