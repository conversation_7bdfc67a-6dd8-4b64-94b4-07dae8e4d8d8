body {
    background-color: #1a1a1a;
    margin: 0;
    padding: 0;
    font-family: "Microsoft YaHei", Arial, sans-serif;
}

.container {
    color: #F2F2F2;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 20px;
}

.title {
    font-size: 28px;
    font-weight: bold;
    color: #0FF796;
    text-shadow: 0 0 5px #0FF796;
}

.data-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.card {
    background-color: #2c2c2c;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.card-title {
    background-color: #0FF796;
    color: #1a1a1a;
    padding: 12px 15px;
    font-size: 20px;
    font-weight: bold;
}

.card-content {
    padding: 15px;
}

.card-content p {
    margin: 10px 0;
    line-height: 1.7;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
}

.card-content strong {
    color: #0FF796;
    margin-right: 10px;
}

.kd-ratios {
    border-top: 1px solid #444;
    margin-top: 10px;
    padding-top: 10px;
}

.kd-ratios p {
    font-size: 14px;
}

.footer {
    text-align: center;
    margin-top: 30px;
    color: #888;
    font-size: 12px;
} 