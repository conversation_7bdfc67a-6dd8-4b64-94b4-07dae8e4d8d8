.font(@name, @file) {
  @font-face {
    font-family: @name;
    src: url("./font/@{file}.woff") format('woff'), url("./font/@{file}.ttf") format('truetype');
  }
}

.font('Number', 'tttgbnumber');
.font('NZBZ', 'NZBZ');
.font('YS', 'HYWH-65W');

@import "base.less";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-user-select: none;
  user-select: none;
}

body {
  font-size: 18px;
  color: #1e1f20;
  font-family: Number, "汉仪文黑-65W", YS, PingFangSC-Medium, "PingFang SC", sans-serif;
  transform: scale(1.4);
  transform-origin: 0 0;
  width: 600px;
}

.container {
  width: 600px;
  padding: 20px 15px 10px 15px;
  background-size: contain;
}


.head-box {
  border-radius: 15px;
  padding: 10px 20px;
  position: relative;
  color: #fff;
  margin-top: 30px;

  .title {
    .font-NZBZ;
    font-size: 36px;
    text-shadow: 0 0 1px #000, 1px 1px 3px rgba(0, 0, 0, .9);

    .label {
      display: inline-block;
      margin-left: 10px;
    }
  }

  .genshin_logo {
    position: absolute;
    top: 1px;
    right: 15px;
    width: 97px;
  }

  .label {
    font-size: 16px;
    text-shadow: 0 0 1px #000, 1px 1px 3px rgba(0, 0, 0, .9);

    span {
      color: #d3bc8e;
      padding: 0 2px;
    }
  }
}


.notice {
  color: #888;
  font-size: 12px;
  text-align: right;
  padding: 12px 5px 5px;
}

.notice-center {
  color: #fff;
  text-align: center;
  margin-bottom: 10px;
  text-shadow: 1px 1px 1px #333;
}

.copyright {
  font-size: 14px;
  text-align: center;
  color: #fff;
  position: relative;
  padding-left: 10px;
  text-shadow: 1px 1px 1px #000;
  margin: 10px 0;

  .version {
    color: #d3bc8e;
    display: inline-block;
    padding: 0 3px;
  }
}


/*  */

.cons {
  display: inline-block;
  vertical-align: middle;
  padding: 0 5px;
  border-radius: 4px;
}


.cons(@idx, @bg, @color:#fff) {
  .cons-@{idx} {
    background: @bg;
    color: @color;
  }
}

.cons(0, #666);
.cons(n0, #404949);
.cons(1, #5cbac2);
.cons(2, #339d61);
.cons(3, #3e95b9);
.cons(4, #3955b7);
.cons(5, #531ba9cf);
.cons(6, #ff5722);

.cons2(@idx, @bg, @color:#fff) {
  .cons2-@{idx} {
    border-radius: 4px;
    background: @bg;
    color: @color;
  }
}

.cons2(0, #666);
.cons2(1, #71b1b7);
.cons2(2, #369961);
.cons2(3, #4596b9);
.cons2(4, #4560b9);
.cons2(5, #531ba9cf);
.cons2(6, #ff5722);

/********  Fetter  ********/

.fetter {
  width: 50px;
  height: 50px;
  display: inline-block;
  background: url('./item/fetter.png');
  background-size: auto 100%;
  @fetters: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10;
  each(@fetters, {
    &.fetter@{value} {
      background-position: (-100%/9)+(100%/9)*@value 0;
    }
  })
}

/********  ELEM  ********/

@elems: hydro, anemo, cryo, electro, geo, pyro, dendro;

each(@elems, {
  .elem-@{value} .talent-icon {
    background-image: url("./bg/talent-@{value}.png");
  }

  .elem-@{value} .elem-bg,
  .@{value}-bg {
    background-image: url("./bg/bg-@{value}.jpg");
  }
})


/* cont */

.cont {
  border-radius: 10px;
  background: url("../common/cont/card-bg.png") top left repeat-x;
  background-size: auto 100%;
  // backdrop-filter: blur(3px);
  margin: 5px 15px 5px 10px;
  position: relative;
  box-shadow: 0 0 1px 0 #ccc, 2px 2px 4px 0 rgba(50, 50, 50, .8);
  overflow: hidden;
  color: #fff;
  font-size: 16px;
}


.cont-title {
  background: rgba(0, 0, 0, .4);
  box-shadow: 0 0 1px 0 #fff;
  color: #d3bc8e;
  padding: 10px 20px;
  text-align: left;
  border-radius: 10px 10px 0 0;

  span {
    font-size: 12px;
    color: #aaa;
    margin-left: 10px;
    font-weight: normal;
  }

  &.border-less {
    background: linear-gradient(rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    box-shadow: none;
    padding-bottom: 5px;
  }
}

.cont-body {
  padding: 10px 15px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 1px 0 #fff;
  font-weight: normal;
}


.cont-footer {
  padding: 10px 15px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.5);
  font-weight: normal;
}

.cont > ul.cont-msg {
  display: block;
  padding: 5px 10px;
  background: rgba(0, 0, 0, 0.5);
}

ul.cont-msg, .cont-footer ul {
  padding-left: 15px;

  li {
    margin: 5px 0;
    margin-left: 15px;

    strong {
      font-weight: normal;
      margin: 0 2px;
      color: #d3bc8e;
    }
  }
}

.cont-table {
  display: table;
  width: 100%;
}

.cont-table .tr {
  display: table-row;
}

.cont-table .tr:nth-child(even) {
  background: rgba(0, 0, 0, .4);
}

.cont-table .tr:nth-child(odd) {
  background: rgba(50, 50, 50, .4);
}

.cont-table .tr > div,
.cont-table .tr > td {
  display: table-cell;
  box-shadow: 0 0 1px 0 #fff;
}

.cont-table .tr > div.value-full {
  display: table;
  width: 200%;
}

.cont-table .tr > div.value-none {
  box-shadow: none;
}

.cont-table .thead {
  text-align: center;
}

.cont-table .thead > div,
.cont-table .thead > td {
  color: #d3bc8e;
  background: rgba(0, 0, 0, .4);
  line-height: 40px;
  height: 40px;
}


.cont-table .title,
.cont-table .th {
  color: #d3bc8e;
  padding-right: 15px;
  text-align: right;
  background: rgba(0, 0, 0, .4);
  min-width: 100px;
  vertical-align: middle;
}

.logo {
  font-size: 18px;
  text-align: center;
  color: #fff;
  margin: 20px 0 10px 0;
}

/* item-icon */
.item-icon {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  position: relative;
  overflow: hidden;

  .img {
    width: 100%;
    height: 100%;
    display: block;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }

  &.artis {
    .img {
      width: 84%;
      height: 84%;
      margin: 8%;
    }
  }

  @stars: 1, 2, 3, 4, 5;
  each(@stars, {
    &.star@{value} {
      background-image: url("../common/item/bg@{value}.png");
    }
    &.opacity-bg.star@{value} {
      background-image: url("../common/item/bg@{value}-o.png");
    }
  })

  &.star-w {
    background: #fff;
  }
}

.item-list {
  display: flex;

  .item-card {
    width: 70px;
    background: #e7e5d9;
  }

  .item-icon {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 12px;
  }

  .item-title {
    color: #222;
    font-size: 13px;
    text-align: center;
    padding: 2px;
    white-space: nowrap;
    overflow: hidden;
  }

  .item-icon {
    height: initial;
  }

  .item-badge {
    position: absolute;
    display: block;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.6);
    font-size: 12px;
    color: #fff;
    padding: 4px 5px 3px;
    border-radius: 0 0 6px 0;
  }
}