import { helpList, helpCfg } from '../config/help.js';
import Render from '../components/Render.js';
import { style } from '../resources/help/imgs/config.js';
import Config from '../components/Config.js';
import fetch from 'node-fetch';
import _ from 'lodash';

export class Help extends plugin {
    constructor() {
        super({
            name: "三角洲行动-帮助",
            event: "message",
            priority: 1008,
            rule: [
                {
                    reg: "^(#三角洲|\\^)(帮助|菜单|功能)$",
                    fnc: "help"
                }
            ]
        });
    }

    async help(e) {
        let helpGroup = [];
        _.forEach(helpList, (group) => {
            _.forEach(group.list, (help) => {
                let icon = help.icon * 1;
                if (!icon) {
                    help.css = 'display:none';
                } else {
                    let x = (icon - 1) % 10;
                    let y = (icon - x - 1) / 10;
                    help.css = `background-position:-${x * 50}px -${y * 50}px`;
                }
            });
            helpGroup.push(group);
        });

        let themeData = await this.getThemeData(helpCfg, helpCfg);
        return await Render.render('help/index.html', {
            helpCfg,
            helpGroup,
            ...themeData,
            element: 'default'
        }, { e, scale: 1.6 });
    }

    async getThemeData(diyStyle, sysStyle) {
        const helpConfig = { ...sysStyle, ...diyStyle };
        const colCount = Math.min(5, Math.max(parseInt(helpConfig?.colCount) || 3, 2));
        const colWidth = Math.min(500, Math.max(100, parseInt(helpConfig?.colWidth) || 265));
        const width = Math.min(2500, Math.max(800, colCount * colWidth + 30));

        // 获取背景图
        const backgroundImage = await this.getBackgroundImage();

        const theme = {
            main: backgroundImage,
            bg: backgroundImage,
            style: style
        };
        const themeStyle = theme.style || {};
        const ret = [`
          body{background-image:url(${theme.bg}) no-repeat;width:${width}px;}
          .container{background-image:url(${theme.main});background-size:cover;}
          .help-table .td,.help-table .th{width:${100 / colCount}%}
          `];
        const css = function (sel, css, key, def, fn) {
            let val = themeStyle[key] ?? diyStyle[key] ?? sysStyle[key] ?? def;
            if (fn) {
                val = fn(val);
            }
            ret.push(`${sel}{${css}:${val}}`);
        };
        css('.help-title,.help-group', 'color', 'fontColor', '#ceb78b');
        css('.help-title,.help-group', 'text-shadow', 'fontShadow', 'none');
        css('.help-desc', 'color', 'descColor', '#eee');
        css('.cont-box', 'background', 'contBgColor', 'rgba(43, 52, 61, 0.8)');
        css('.cont-box', 'backdrop-filter', 'contBgBlur', 3, (n) => diyStyle.bgBlur === false ? 'none' : `blur(${n}px)`);
        css('.help-group', 'background', 'headerBgColor', 'rgba(34, 41, 51, .4)');
        css('.help-table .tr:nth-child(odd)', 'background', 'rowBgColor1', 'rgba(34, 41, 51, .2)');
        css('.help-table .tr:nth-child(even)', 'background', 'rowBgColor2', 'rgba(34, 41, 51, .4)');
        return {
            style: `<style>${ret.join('\n')}</style>`,
            colCount
        };
    }

    /**
     * 获取背景图片
     * @returns {Promise<string>} 背景图片路径或URL
     */
    async getBackgroundImage() {
        try {
            const config = Config.getConfig();
            const helpBgConfig = config?.help_background;

            // 如果未启用自定义API或配置不存在，使用默认背景
            if (!helpBgConfig?.use_custom_api || !helpBgConfig?.api_url) {
                return `../imgs/${helpBgConfig?.fallback_image || 'bg.jpg'}`;
            }

            // 尝试从API获取背景图
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), helpBgConfig.timeout || 5000);

            const response = await fetch(helpBgConfig.api_url, {
                method: 'GET',
                signal: controller.signal,
                headers: {
                    'User-Agent': 'Delta-Force-Plugin/1.2.1'
                }
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.startsWith('image/')) {
                    logger.info('[DELTA FORCE PLUGIN] 成功获取自定义背景图');
                    return helpBgConfig.api_url;
                } else {
                    // 如果返回的不是图片，尝试解析JSON获取图片URL
                    const data = await response.json();
                    if (data.url || data.image || data.data) {
                        const imageUrl = data.url || data.image || data.data;
                        logger.info('[DELTA FORCE PLUGIN] 成功获取自定义背景图URL');
                        return imageUrl;
                    }
                }
            }

            logger.warn('[DELTA FORCE PLUGIN] 自定义背景图API请求失败，使用默认背景');
            return `../imgs/${helpBgConfig.fallback_image || 'bg.jpg'}`;

        } catch (error) {
            if (error.name === 'AbortError') {
                logger.warn('[DELTA FORCE PLUGIN] 自定义背景图API请求超时，使用默认背景');
            } else {
                logger.error('[DELTA FORCE PLUGIN] 获取自定义背景图时发生错误:', error.message);
            }

            const config = Config.getConfig();
            const fallbackImage = config?.help_background?.fallback_image || 'bg.jpg';
            return `../imgs/${fallbackImage}`;
        }
    }
}
