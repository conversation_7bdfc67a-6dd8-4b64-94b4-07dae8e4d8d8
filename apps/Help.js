import { helpList, helpCfg } from '../config/help.js';
import Render from '../components/Render.js';
import { style } from '../resources/help/imgs/config.js';
import _ from 'lodash';

export class Help extends plugin {
    constructor() {
        super({
            name: "三角洲行动-帮助",
            event: "message",
            priority: 1008,
            rule: [
                {
                    reg: "^(#三角洲|\\^)(帮助|菜单|功能)$",
                    fnc: "help"
                }
            ]
        });
    }

    async help(e) {
        let helpGroup = [];
        _.forEach(helpList, (group) => {
            _.forEach(group.list, (help) => {
                let icon = help.icon * 1;
                if (!icon) {
                    help.css = 'display:none';
                } else {
                    let x = (icon - 1) % 10;
                    let y = (icon - x - 1) / 10;
                    help.css = `background-position:-${x * 50}px -${y * 50}px`;
                }
            });
            helpGroup.push(group);
        });

        let themeData = await this.getThemeData(helpCfg, helpCfg);
        return await Render.render('help/index.html', {
            helpCfg,
            helpGroup,
            ...themeData,
            element: 'default'
        }, { e, scale: 1.6 });
    }

    async getThemeData(diyStyle, sysStyle) {
        const helpConfig = { ...sysStyle, ...diyStyle };
        const colCount = Math.min(5, Math.max(parseInt(helpConfig?.colCount) || 3, 2));
        const colWidth = Math.min(500, Math.max(100, parseInt(helpConfig?.colWidth) || 265));
        const width = Math.min(2500, Math.max(800, colCount * colWidth + 30));
        const theme = {
            main: `../imgs/bg.jpg`,
            bg: `../imgs/bg.jpg`,
            style: style
        };
        const themeStyle = theme.style || {};
        const ret = [`
          body{background-image:url(${theme.bg}) no-repeat;width:${width}px;}
          .container{background-image:url(${theme.main});background-size:cover;}
          .help-table .td,.help-table .th{width:${100 / colCount}%}
          `];
        const css = function (sel, css, key, def, fn) {
            let val = themeStyle[key] ?? diyStyle[key] ?? sysStyle[key] ?? def;
            if (fn) {
                val = fn(val);
            }
            ret.push(`${sel}{${css}:${val}}`);
        };
        css('.help-title,.help-group', 'color', 'fontColor', '#ceb78b');
        css('.help-title,.help-group', 'text-shadow', 'fontShadow', 'none');
        css('.help-desc', 'color', 'descColor', '#eee');
        css('.cont-box', 'background', 'contBgColor', 'rgba(43, 52, 61, 0.8)');
        css('.cont-box', 'backdrop-filter', 'contBgBlur', 3, (n) => diyStyle.bgBlur === false ? 'none' : `blur(${n}px)`);
        css('.help-group', 'background', 'headerBgColor', 'rgba(34, 41, 51, .4)');
        css('.help-table .tr:nth-child(odd)', 'background', 'rowBgColor1', 'rgba(34, 41, 51, .2)');
        css('.help-table .tr:nth-child(even)', 'background', 'rowBgColor2', 'rgba(34, 41, 51, .4)');
        return {
            style: `<style>${ret.join('\n')}</style>`,
            colCount
        };
    }
}
